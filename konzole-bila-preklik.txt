> fetch('/modules/priceinquiry/test_on_product.js')
  .then(r => r.text())
  .then(code => eval(code));
< Promise {status: "pending"}
[Log] === PriceInquiry Test na stránce produktu ===
[Log] 1. Test existence objektů:
[Log]   PriceInquiryJS: – "✓ EXISTS"
[Log]   prestashop: – "✓ EXISTS"
[Log] 2. Test našich bloků:
[Log]   Nale<PERSON>o bloků: – 1
[Log]   Blok 0: – {visible: true, initialPrice: "0", position: DOMRect}
{visible: true, initialPrice: "0", position: DOMRect}Object
[Log] 3. Test cenových elementů:
[Log]   Hledám všechny elementy s "Kč":
[Log]   Nalezeno 19 cenových elementů:
[Log]     [0] DIV.shopping-cart: "0
			Košík 0,00 Kč"
[Log]         Selector: .shopping-cart
[Log]     [1] SPAN.cart-products-count hidden-sm-down: "Košík 0,00 Kč"
[Log]         Selector: .cart-products-count.hidden-sm-down
[Log]     [2] SPAN.value: "0,00 Kč"
[Log]         Selector: .value
[Log]     [3] DIV.product-price-and-shipping: "20,90 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [4] SPAN.price: "20,90 Kč"
[Log]         Selector: .price
[Log]     [5] DIV.product-price-and-shipping: "44,00 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [6] SPAN.price: "44,00 Kč"
[Log]         Selector: .price
[Log]     [7] DIV.product-price-and-shipping: "13,60 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [8] SPAN.price: "13,60 Kč"
[Log]         Selector: .price
[Log]     [9] DIV.product-price-and-shipping: "0,00 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [10] SPAN.price: "0,00 Kč"
[Log]         Selector: .price
[Log]     [11] DIV.product-price-and-shipping: "70,20 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [12] SPAN.price: "70,20 Kč"
[Log]         Selector: .price
[Log]     [13] DIV.product-price-and-shipping: "13,60 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [14] SPAN.price: "13,60 Kč"
[Log]         Selector: .price
[Log]     [15] DIV.product-price-and-shipping: "30,90 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [16] SPAN.price: "30,90 Kč"
[Log]         Selector: .price
[Log]     [17] DIV.product-price-and-shipping: "8,00 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [18] SPAN.price: "8,00 Kč"
[Log]         Selector: .price
[Log]   Test původních selektorů:
[Log]     .product-prices .price: 0 elementů
[Log]     .current-price .price: 0 elementů
[Log]     .product-price .price: 0 elementů
[Log]     .price: 8 elementů
[Log] 4. Test funkčnosti:
[Log]   Spouštím checkAndUpdatePriceInquiry...
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log]   Testuju event systém...
[Log] PriceInquiry: updatedProduct event (front.js, line 54)
[Log] PriceInquiry: handleProductUpdate called – {test: true} (front.js, line 61)
[Log] 5. Test variant selektorů:
[Log]   select[name*="group"]: 0 elementů
[Log]   input[name*="group"]: 2 elementů
[Log]     [0] INPUT name="group[2]" value="8"
[Log]     [1] INPUT name="group[2]" value="11"
[Log]   .product-variants select: 0 elementů
[Log]   .product-variants input: 2 elementů
[Log]     [0] INPUT name="group[2]" value="8"
[Log]     [1] INPUT name="group[2]" value="11"
[Log]   [data-product-attribute]: 2 elementů
[Log]     [0] INPUT name="group[2]" value="8"
[Log]     [1] INPUT name="group[2]" value="11"
[Log] 6. Spouštím monitoring změn...
[Log] === Test dokončen - sledování pokračuje ===
[Log] Pro zastavení monitoring: observer.disconnect()
[Log] Dostupné manuální testy:
[Log]   testPriceInquiry.showBlock()
[Log]   testPriceInquiry.hideBlock()
[Log]   testPriceInquiry.checkPrice()
[Log]   testPriceInquiry.simulateVariantChange()
[Log] Změna 209: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 210: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 211: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 212: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 213: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 214: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 215: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 216: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 217: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 218: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 219: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 220: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 221: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 222: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 223: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 224: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 225: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 226: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 227: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 228: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 229: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 230: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 231: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 232: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 1: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 233: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 234: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 235: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 236: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 237: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 238: – "childList" – 
<div class="zoomWindowContainer" style="width: 350px;">…</div>

<div class="zoomWindowContainer" style="width: 350px;">…</div>
[Log] Změna 239: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66614-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66614-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 2: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 3: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 4: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 5: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 6: – "childList" – 
<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="531b216a-f030-4c03-b014-732e6bff513a" style="position: absolute; top: 363.5625px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 7: – "childList" – 
<div class="zoomWindowContainer" style="width: 350px;">…</div>

<div class="zoomWindowContainer" style="width: 350px;">…</div>
[Log] Změna 8: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66614-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66614-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 240: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 9: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000774%; overflow: hidden;"></div>
[Log] Změna 241: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 10: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 242: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000414%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000414%; overflow: hidden;"></div>
[Log] Změna 11: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000414%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000414%; overflow: hidden;"></div>
[Log] Změna 243: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000205%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000205%; overflow: hidden;"></div>
[Log] Změna 12: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000205%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000205%; overflow: hidden;"></div>
[Log] Změna 244: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000042%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000042%; overflow: hidden;"></div>
[Log] Změna 13: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000042%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000042%; overflow: hidden;"></div>
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log] Změna 245: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 246: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 14: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 15: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x53)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)