> fetch('/modules/priceinquiry/test_on_product.js')
  .then(r => r.text())
  .then(code => eval(code));
< Promise {status: "pending"}
[Log] === PriceInquiry Test na stránce produktu ===
[Log] 1. Test existence objektů:
[Log]   PriceInquiryJS: – "✓ EXISTS"
[Log]   prestashop: – "✓ EXISTS"
[Log] 2. Test našich bloků:
[Log]   Nale<PERSON><PERSON> bloků: – 1
[Log]   Blok 0: – {visible: false, initialPrice: "91.4", position: DOMRect}
{visible: false, initialPrice: "91.4", position: DOMRect}Object
[Log] 3. Test cenových elementů:
[Log]   Hledám všechny elementy s "Kč":
[Log]   Nalezeno 22 cenových elementů:
[Log]     [0] DIV.shopping-cart: "0
			Košík 0,00 Kč"
[Log]         Selector: .shopping-cart
[Log]     [1] SPAN.cart-products-count hidden-sm-down: "Košík 0,00 Kč"
[Log]         Selector: .cart-products-count.hidden-sm-down
[Log]     [2] SPAN.value: "0,00 Kč"
[Log]         Selector: .value
[Log]     [3] DIV.product-price : "91,40 Kč"
[Log]         Selector: .product-price
[Log]     [4] DIV.current-price: "91,40 Kč"
[Log]         Selector: .current-price
[Log]     [5] SPAN.current-price-value: "91,40 Kč"
[Log]         Selector: .current-price-value
[Log]     [6] DIV.product-price-and-shipping: "9,50 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [7] SPAN.price: "9,50 Kč"
[Log]         Selector: .price
[Log]     [8] DIV.product-price-and-shipping: "11,00 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [9] SPAN.price: "11,00 Kč"
[Log]         Selector: .price
[Log]     [10] DIV.product-price-and-shipping: "13,40 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [11] SPAN.price: "13,40 Kč"
[Log]         Selector: .price
[Log]     [12] DIV.product-price-and-shipping: "21,40 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [13] SPAN.price: "21,40 Kč"
[Log]         Selector: .price
[Log]     [14] DIV.product-price-and-shipping: "54,30 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [15] SPAN.price: "54,30 Kč"
[Log]         Selector: .price
[Log]     [16] DIV.product-price-and-shipping: "16,70 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [17] SPAN.price: "16,70 Kč"
[Log]         Selector: .price
[Log]     [18] DIV.product-price-and-shipping: "20,90 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [19] SPAN.price: "20,90 Kč"
[Log]         Selector: .price
[Log]     [20] DIV.product-price-and-shipping: "5,10 Kč"
[Log]         Selector: .product-price-and-shipping
[Log]     [21] SPAN.price: "5,10 Kč"
[Log]         Selector: .price
[Log]   Test původních selektorů:
[Log]     .product-prices .price: 0 elementů
[Log]     .current-price .price: 0 elementů
[Log]     .product-price .price: 0 elementů
[Log]     .price: 8 elementů
[Log] 4. Test funkčnosti:
[Log]   Spouštím checkAndUpdatePriceInquiry...
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "91.4" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log]   Testuju event systém...
[Log] PriceInquiry: updatedProduct event (front.js, line 54)
[Log] PriceInquiry: handleProductUpdate called – {test: true} (front.js, line 61)
[Log] 5. Test variant selektorů:
[Log]   select[name*="group"]: 0 elementů
[Log]   input[name*="group"]: 2 elementů
[Log]     [0] INPUT name="group[2]" value="8"
[Log]     [1] INPUT name="group[2]" value="11"
[Log]   .product-variants select: 0 elementů
[Log]   .product-variants input: 2 elementů
[Log]     [0] INPUT name="group[2]" value="8"
[Log]     [1] INPUT name="group[2]" value="11"
[Log]   [data-product-attribute]: 2 elementů
[Log]     [0] INPUT name="group[2]" value="8"
[Log]     [1] INPUT name="group[2]" value="11"
[Log] 6. Spouštím monitoring změn...
[Log] === Test dokončen - sledování pokračuje ===
[Log] Pro zastavení monitoring: observer.disconnect()
[Log] Dostupné manuální testy:
[Log]   testPriceInquiry.showBlock()
[Log]   testPriceInquiry.hideBlock()
[Log]   testPriceInquiry.checkPrice()
[Log]   testPriceInquiry.simulateVariantChange()
[Log] Změna 1: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000863%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000863%; overflow: hidden;"></div>
[Log] Změna 2: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 3: – "childList" – 
<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 4: – "childList" – 
<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 5: – "childList" – 
<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 6: – "childList" – 
<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="3c3b6966-606c-40eb-ad5b-72137e5bd149" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 7: – "childList" – 
<div class="zoomWindowContainer" style="width: 350px;">…</div>

<div class="zoomWindowContainer" style="width: 350px;">…</div>
[Log] Změna 8: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66614-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66614-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 9: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000525%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000525%; overflow: hidden;"></div>
[Log] Změna 10: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000306%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000306%; overflow: hidden;"></div>
[Log] Změna 11: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.00011%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.00011%; overflow: hidden;"></div>
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "91.4" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log] Změna 12: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000005%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000005%; overflow: hidden;"></div>
[Log] Změna 13: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 14: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log]   Po změně: 1 bloků, 0 viditelných (x14)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "91.4" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)