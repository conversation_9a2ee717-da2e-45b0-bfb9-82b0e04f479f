/**
 * CSS pro frontend modul "Cena na dotaz"
 * Verze 3.0.0
 */

/* --- <PERSON><PERSON>y pro přehled produktů --- */
.pi-category-text {
    font-weight: bold;
    color: #333;
    margin-top: 5px;
}

/* --- Styly pro detail produktu --- */
.price-inquiry-block {
    margin-top: 15px;
    text-align: left;
}

.price-inquiry-block-detail {
    order: -1; /* Zobrazí se před ostatními flex polo<PERSON>mi, jako je wishlist */
    width: 100%;
    margin-bottom: 15px;
}

.pi-price-text-wrapper {
    margin-bottom: 15px;
}

.pi-price-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

.pi-container-simple .pi-btn {
    margin-bottom: 10px;
}

.pi-info-simple {
    font-size: 0.9rem;
    color: #6c757d;
}

/* --- Obecné styly pro tlačítko --- */
.pi-btn {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.pi-btn:hover {
    background-color: #0056b3;
}

/* --- Styly pro modal okno s unikátními třídami --- */
.pi-modal,
.pi-price-inquiry-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 99999 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    box-sizing: border-box !important;
}

.pi-modal[style*="block"],
.pi-price-inquiry-modal[style*="block"] {
    display: flex !important;
}

.pi-modal-overlay,
.pi-price-inquiry-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.6) !important;
    cursor: pointer !important;
    backdrop-filter: blur(2px) !important;
}

.pi-modal-content,
.pi-price-inquiry-content {
    position: relative !important;
    background: #ffffff !important;
    border-radius: 12px !important;
    max-width: 650px !important;
    width: 95% !important;
    max-height: 95vh !important;
    overflow-y: auto !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    z-index: 1 !important;
    margin: 20px !important;
    box-sizing: border-box !important;
    padding: 0 !important;
}

.pi-modal-header {
    padding: 25px 30px 20px !important;
    border-bottom: 1px solid #e9ecef !important;
    position: relative !important;
    background: #f8f9fa !important;
    border-radius: 12px 12px 0 0 !important;
}

.pi-modal-header h3 {
    margin: 0 !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    color: #333 !important;
    padding-right: 40px !important;
}

.pi-modal-close {
    position: absolute !important;
    top: 20px !important;
    right: 25px !important;
    background: none !important;
    border: none !important;
    font-size: 24px !important;
    cursor: pointer !important;
    color: #666 !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    transition: all 0.2s ease !important;
}

.pi-modal-close:hover {
    background: #e9ecef !important;
    color: #333 !important;
}

.pi-modal-body {
    padding: 30px !important;
}

.pi-product-info {
    display: flex !important;
    gap: 20px !important;
    margin-bottom: 25px !important;
    padding: 20px !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    border: 1px solid #e9ecef !important;
}

.pi-product-image {
    flex-shrink: 0 !important;
}

.pi-product-image img {
    width: 80px !important;
    height: 80px !important;
    object-fit: cover !important;
    border-radius: 8px !important;
    border: 1px solid #dee2e6 !important;
}

.pi-product-details {
    flex: 1 !important;
}

.pi-product-details h4 {
    margin: 0 0 10px 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #333 !important;
    line-height: 1.3 !important;
}

.pi-product-reference {
    margin: 0 !important;
    font-size: 14px !important;
    color: #666 !important;
}

.pi-form-group {
    margin-bottom: 20px !important;
}

.pi-form-group label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 500 !important;
    color: #333 !important;
    font-size: 14px !important;
}

.pi-form-control {
    width: 100% !important;
    padding: 12px 16px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    transition: border-color 0.2s ease !important;
    box-sizing: border-box !important;
    font-family: inherit !important;
}

.pi-form-control:focus {
    outline: none !important;
    border-color: #007cba !important;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1) !important;
}

.pi-required-star {
    color: #dc3545 !important;
}

.pi-checkbox-group {
    margin: 25px 0 !important;
}

.pi-checkbox-label {
    display: flex !important;
    align-items: flex-start !important;
    gap: 12px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
}

.pi-checkbox-label input[type="checkbox"] {
    margin: 0 !important;
    width: 18px !important;
    height: 18px !important;
    flex-shrink: 0 !important;
    margin-top: 2px !important;
}

.pi-modal-footer {
    padding: 25px 30px !important;
    border-top: 1px solid #e9ecef !important;
    background: #f8f9fa !important;
    border-radius: 0 0 12px 12px !important;
    text-align: right !important;
}

.pi-btn-modal {
    background: #007cba !important;
    color: white !important;
    border: none !important;
    padding: 12px 30px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-width: 140px !important;
}

.pi-btn-modal:hover {
    background: #005a87 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3) !important;
}

.pi-btn-modal:disabled {
    background: #6c757d !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

.pi-messages {
    margin: 20px 0 !important;
}

.pi-alert {
    padding: 12px 16px !important;
    border-radius: 8px !important;
    margin-bottom: 15px !important;
    font-size: 14px !important;
}

.pi-alert-danger {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

.pi-alert-success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

/* Responzivní design */
@media (max-width: 768px) {
    .pi-price-inquiry-content,
    .pi-modal-content {
        width: 98% !important;
        margin: 10px !important;
        max-height: 98vh !important;
    }

    .pi-modal-header,
    .pi-modal-body,
    .pi-modal-footer {
        padding: 20px !important;
    }

    .pi-product-info {
        flex-direction: column !important;
        gap: 15px !important;
    }

    .pi-product-image img {
        width: 60px !important;
        height: 60px !important;
    }
}