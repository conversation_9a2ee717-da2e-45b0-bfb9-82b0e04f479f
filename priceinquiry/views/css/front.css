/**
 * CSS pro frontend modul "Cena na dotaz"
 * Verze 3.0.0
 */

/* --- <PERSON><PERSON><PERSON> pro přehled produktů --- */
.pi-category-text {
    font-weight: bold;
    color: #333;
    margin-top: 5px;
}

/* --- Styly pro detail produktu --- */
.price-inquiry-block {
    margin-top: 15px;
    text-align: left;
}

.price-inquiry-block-detail {
    order: -1; /* Zobrazí se před ostatními flex polo<PERSON>mi, jako je wishlist */
    width: 100%;
    margin-bottom: 15px;
}

.pi-price-text-wrapper {
    margin-bottom: 15px;
}

.pi-price-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

.pi-container-simple .pi-btn {
    margin-bottom: 10px;
}

.pi-info-simple {
    font-size: 0.9rem;
    color: #6c757d;
}

/* --- Obecné styly pro tlačítko --- */
.pi-btn {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.pi-btn:hover {
    background-color: #0056b3;
}

/* --- Styly pro modal okno (zůstávají stejné) --- */
.pi-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
    background-color: rgba(0, 0, 0, 0.5);
}

.pi-modal-content {
    position: relative;
    background: white;
    margin: 5% auto;
    padding: 20px;
    width: 90%;
    max-width: 500px;
    border-radius: 5px;
}

.pi-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}