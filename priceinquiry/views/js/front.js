/**
 * JavaScript pro frontend modul "Cena na dotaz"
 * Verze 6.0.0 - Obsluhuje modal a reaguje na změny variant produktu.
 */
(function() {
    "use strict";

    const PriceInquiryJS = {
        init: function() {
            if (this.initialized) return;
            this.initialized = true;
            this.bindEvents();
            this.bindPrestaShopEvents();
        },

        bindEvents: function() {
            document.body.addEventListener('click', (e) => {
                const inquiryButton = e.target.closest('.pi-btn');
                if (inquiryButton) {
                    e.preventDefault();
                    this.handleInquiryClick(inquiryButton);
                    return;
                }

                const closeModalButton = e.target.closest('.pi-modal-close, .pi-modal-overlay');
                if (closeModalButton) {
                    e.preventDefault();
                    this.closeModal();
                }
            });

            document.body.addEventListener('submit', (e) => {
                if (e.target.id === 'piForm') {
                    e.preventDefault();
                    // Zde bude logika pro odes<PERSON>ání formulář<PERSON>
                }
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && document.getElementById('piModal')?.style.display !== 'none') {
                    this.closeModal();
                }
            });
        },

        bindPrestaShopEvents: function() {
            // Reagujeme na změny variant produktu
            if (typeof prestashop !== 'undefined') {
                prestashop.on('updateProduct', (event) => {
                    console.log('PriceInquiry: updateProduct event');
                });

                prestashop.on('updatedProduct', (event) => {
                    console.log('PriceInquiry: updatedProduct event');
                    this.handleProductUpdate(event);
                });
            }

            // Záložní řešení - posloucháme změny na variant selectu
            this.setupVariantListener();
        },

        setupVariantListener: function() {
            console.log('PriceInquiry: Nastavuji variant listener...');

            // Zkusíme najít variant select několikrát
            const trySetupListener = (attempt = 1) => {
                const variantSelect = document.querySelector('select[name="group[1]"]');

                if (variantSelect) {
                    console.log(`PriceInquiry: Variant select nalezen (pokus ${attempt}), přidávám listener`);

                    // Odebereme starý listener pokud existuje
                    if (variantSelect._piListener) {
                        variantSelect.removeEventListener('change', variantSelect._piListener);
                    }

                    // Přidáme nový listener
                    const listener = (event) => {
                        console.log('PriceInquiry: *** VARIANT SELECT CHANGED ***', event.target.value);
                        console.log('PriceInquiry: Spouštím handleProductUpdate...');

                        // Počkáme na AJAX aktualizaci v několika vlnách
                        setTimeout(() => {
                            console.log('PriceInquiry: 100ms timeout - kontroluji...');
                            this.handleProductUpdate({ reason: 'variant_select_change_100ms' });
                        }, 100);

                        setTimeout(() => {
                            console.log('PriceInquiry: 500ms timeout - kontroluji...');
                            this.handleProductUpdate({ reason: 'variant_select_change_500ms' });
                        }, 500);

                        setTimeout(() => {
                            console.log('PriceInquiry: 1000ms timeout - kontroluji...');
                            this.handleProductUpdate({ reason: 'variant_select_change_1000ms' });
                        }, 1000);
                    };

                    variantSelect.addEventListener('change', listener);
                    variantSelect._piListener = listener; // Uložíme referenci

                    console.log('PriceInquiry: Event listener přidán úspěšně');
                    return true;
                } else {
                    console.log(`PriceInquiry: Variant select nenalezen (pokus ${attempt})`);

                    // Zkusíme to znovu za chvilku (max 5 pokusů)
                    if (attempt < 5) {
                        setTimeout(() => trySetupListener(attempt + 1), 200);
                    } else {
                        console.log('PriceInquiry: Variant select nenalezen ani po 5 pokusech');
                    }
                    return false;
                }
            };

            // Spustíme první pokus
            trySetupListener();

            // Přidáme také obecný listener na všechny selecty pro jistotu
            document.addEventListener('change', (event) => {
                if (event.target.tagName === 'SELECT' && event.target.name && event.target.name.includes('group')) {
                    console.log('PriceInquiry: *** OBECNÝ SELECT CHANGE ***', event.target.name, event.target.value);
                    setTimeout(() => {
                        this.handleProductUpdate({ reason: 'generic_select_change' });
                    }, 300);
                }
            });

            // Přidáme MutationObserver jako záložní řešení
            this.setupMutationObserver();
        },

        setupMutationObserver: function() {
            console.log('PriceInquiry: Nastavuji MutationObserver...');

            this.observer = new MutationObserver((mutations) => {
                let shouldCheck = false;

                mutations.forEach((mutation) => {
                    // Sledujeme změny v cenových elementech
                    if (mutation.target.classList &&
                        (mutation.target.classList.contains('product-price') ||
                         mutation.target.classList.contains('product-prices'))) {
                        console.log('PriceInquiry: *** MUTATION v cenovém elementu ***', mutation.type);
                        shouldCheck = true;
                    }

                    // Sledujeme změny v product-add-to-cart sekci
                    if (mutation.target.classList &&
                        mutation.target.classList.contains('product-add-to-cart')) {
                        console.log('PriceInquiry: *** MUTATION v add-to-cart ***', mutation.type);
                        shouldCheck = true;
                    }

                    // Sledujeme přidání nových elementů s cenou
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === 1 && node.textContent && node.textContent.includes('Kč')) {
                                console.log('PriceInquiry: *** PŘIDÁN element s cenou ***', node);
                                shouldCheck = true;
                            }
                        });
                    }
                });

                if (shouldCheck) {
                    console.log('PriceInquiry: MutationObserver detekoval změnu, spouštím kontrolu...');
                    setTimeout(() => {
                        this.handleProductUpdate({ reason: 'mutation_observer' });
                    }, 100);
                }
            });

            // Sledujeme celý dokument
            this.observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'style']
            });

            console.log('PriceInquiry: MutationObserver nastaven');
        },

        handleProductUpdate: function(event) {
            console.log('PriceInquiry: *** handleProductUpdate CALLED ***', event);

            // Okamžitá kontrola
            console.log('PriceInquiry: Okamžitá kontrola...');
            this.checkAndUpdatePriceInquiry();

            // Kontrola po 100ms
            setTimeout(() => {
                console.log('PriceInquiry: Kontrola po 100ms...');
                this.checkAndUpdatePriceInquiry();
            }, 100);

            // Kontrola po 300ms
            setTimeout(() => {
                console.log('PriceInquiry: Kontrola po 300ms...');
                this.checkAndUpdatePriceInquiry();
            }, 300);

            // Kontrola po 500ms
            setTimeout(() => {
                console.log('PriceInquiry: Kontrola po 500ms...');
                this.checkAndUpdatePriceInquiry();
            }, 500);

            // Kontrola po 1000ms
            setTimeout(() => {
                console.log('PriceInquiry: Kontrola po 1000ms...');
                this.checkAndUpdatePriceInquiry();
            }, 1000);

            // Kontrola po 2000ms pro jistotu
            setTimeout(() => {
                console.log('PriceInquiry: Finální kontrola po 2000ms...');
                this.checkAndUpdatePriceInquiry();
            }, 2000);
        },

        checkAndUpdatePriceInquiry: function() {
            console.log('PriceInquiry: Spouštím kontrolu ceny...');

            // Nejdříve zkontrolujeme, jestli existuje náš blok
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (!priceInquiryBlock) {
                console.log('PriceInquiry: Blok .price-inquiry-block-detail nenalezen - modul pravděpodobně není aktivní na této stránce');
                return;
            }

            // Zkontrolujeme počáteční cenu z data atributu
            const initialPrice = priceInquiryBlock.getAttribute('data-initial-price');
            console.log('PriceInquiry: Počáteční cena z PHP:', initialPrice);

            // Pokud je blok aktuálně viditelný, znamená to, že PHP detekoval nulovou cenu
            const isCurrentlyVisible = priceInquiryBlock.style.display !== 'none' &&
                                     window.getComputedStyle(priceInquiryBlock).display !== 'none';

            console.log('PriceInquiry: Blok je aktuálně viditelný:', isCurrentlyVisible);

            // Najdeme aktuální cenu produktu - použijeme správné selektory z analýzy
            const priceSelectors = [
                '.product-price',           // Hlavní cenový element
                '.product-prices .price',   // Záložní
                '.current-price .price'     // Záložní
            ];

            let priceElement = null;
            let priceText = '';

            // Nejdříve vypíšeme všechny nalezené elementy pro debug
            console.log('PriceInquiry: === DEBUG všech cenových elementů ===');
            priceSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                console.log(`  ${selector}: ${elements.length} elementů`);
                elements.forEach((el, i) => {
                    const text = el.textContent || el.innerText || '';
                    const visible = window.getComputedStyle(el).display !== 'none';
                    console.log(`    [${i}] text: "${text.trim()}" visible: ${visible}`);
                });
            });

            // Nyní najdeme nejlepší element
            for (const selector of priceSelectors) {
                const elements = document.querySelectorAll(selector);

                for (const element of elements) {
                    const text = element.textContent || element.innerText || '';
                    const visible = window.getComputedStyle(element).display !== 'none';

                    // Hledáme viditelný element s cenou
                    if (text.trim() && text.includes('Kč') && !text.includes('ušetříte') && visible) {
                        priceElement = element;
                        priceText = text;
                        console.log('PriceInquiry: Nalezen VIDITELNÝ cenový element:', selector, 'text:', priceText);
                        break;
                    }
                }
                if (priceElement) break;
            }

            // Pokud jsme nenašli viditelný, zkusíme jakýkoliv
            if (!priceElement) {
                console.log('PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv...');
                for (const selector of priceSelectors) {
                    const elements = document.querySelectorAll(selector);

                    for (const element of elements) {
                        const text = element.textContent || element.innerText || '';
                        if (text.trim() && text.includes('Kč') && !text.includes('ušetříte')) {
                            priceElement = element;
                            priceText = text;
                            console.log('PriceInquiry: Nalezen JAKÝKOLIV cenový element:', selector, 'text:', priceText);
                            break;
                        }
                    }
                    if (priceElement) break;
                }
            }

            if (!priceElement || !priceText.trim()) {
                console.log('PriceInquiry: Cenový element nenalezen, čekám na aktualizaci...');
                // Zkusíme to znovu za chvilku
                setTimeout(() => {
                    console.log('PriceInquiry: Opakuji kontrolu po 200ms...');
                    this.checkAndUpdatePriceInquiry();
                }, 200);
                return;
            }

            // Získáme číselnou hodnotu z textu ceny
            const priceMatch = priceText.match(/[\d,\.]+/);
            let priceValue = 0;

            if (priceMatch) {
                // Nahradíme čárku tečkou pro správný parsing
                const cleanPrice = priceMatch[0].replace(',', '.');
                priceValue = parseFloat(cleanPrice);
            }

            console.log('PriceInquiry: Detekovaná cena:', priceValue, 'z textu:', priceText);

            // Kontrolujeme, zda je cena nulová
            const isZeroPrice = priceValue === 0 ||
                               priceText.includes('0,00') ||
                               priceText.includes('0.00') ||
                               priceText.trim() === '0';

            console.log('PriceInquiry: Je cena nulová?', isZeroPrice);

            if (isZeroPrice && !isCurrentlyVisible) {
                // Cena je nulová a blok není viditelný - zobrazíme ho
                console.log('PriceInquiry: Zobrazuji blok (cena je nulová)');
                this.showPriceInquiry();
            } else if (!isZeroPrice && isCurrentlyVisible) {
                // Cena není nulová a blok je viditelný - skryjeme ho
                console.log('PriceInquiry: Skrývám blok (cena není nulová)');
                this.hidePriceInquiry();
            } else {
                console.log('PriceInquiry: Ponechávám současný stav');
            }
        },

        showPriceInquiry: function() {
            console.log('PriceInquiry: Zobrazuji "cena na dotaz"');

            // Přidáme CSS styly pro skrytí ceny a tlačítek
            this.applyCssHiding(true);

            // Zobrazíme náš blok (pokud existuje)
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (priceInquiryBlock) {
                priceInquiryBlock.style.display = 'block';
                console.log('PriceInquiry: Blok zobrazen');
            } else {
                console.log('PriceInquiry: Blok nenalezen!');
            }
        },

        hidePriceInquiry: function() {
            console.log('PriceInquiry: Skrývám "cena na dotaz"');

            // Odstraníme CSS styly pro skrytí ceny a tlačítek
            this.applyCssHiding(false);

            // Skryjeme náš blok
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (priceInquiryBlock) {
                priceInquiryBlock.style.display = 'none';
                console.log('PriceInquiry: Blok skryt');
            }
        },

        applyCssHiding: function(hide) {
            console.log('PriceInquiry: applyCssHiding:', hide);

            // Odstraníme všechny naše CSS styly
            const existingStyles = document.querySelectorAll('#pi-dynamic-styles, style[data-pi-generated]');
            existingStyles.forEach(style => style.remove());

            if (hide) {
                // Přidáme nové CSS pro skrytí
                const styleElement = document.createElement('style');
                styleElement.id = 'pi-dynamic-styles';
                styleElement.setAttribute('data-pi-generated', 'true');
                styleElement.textContent = `
                    .product-price,
                    .product-prices { display: none !important; }
                    .product-add-to-cart .product-quantity,
                    .product-add-to-cart .add { display: none !important; }
                `;
                document.head.appendChild(styleElement);
                console.log('PriceInquiry: CSS pro skrytí aplikováno');
            } else {
                // Ujistíme se, že jsou cenové elementy viditelné
                const priceElements = document.querySelectorAll('.product-price, .product-prices');
                priceElements.forEach(el => {
                    el.style.display = '';
                });

                const cartElements = document.querySelectorAll('.product-add-to-cart .product-quantity, .product-add-to-cart .add');
                cartElements.forEach(el => {
                    el.style.display = '';
                });
                console.log('PriceInquiry: CSS pro skrytí odstraněno, elementy obnoveny');
            }
        },

        checkInitialState: function() {
            console.log('PriceInquiry: Kontroluji počáteční stav...');

            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (!priceInquiryBlock) {
                console.log('PriceInquiry: Blok nenalezen, ukončuji kontrolu');
                return;
            }

            const showBlock = priceInquiryBlock.getAttribute('data-show-block');
            const initialPrice = parseFloat(priceInquiryBlock.getAttribute('data-initial-price')) || 0;

            console.log('PriceInquiry: Počáteční stav - show_block:', showBlock, 'price:', initialPrice);

            // Pokud by měl být blok skrytý, ale CSS ho drží viditelný, opravíme to
            if (showBlock === 'false' || initialPrice > 0) {
                console.log('PriceInquiry: Blok by měl být skrytý, kontroluji...');

                // Zkontrolujeme, jestli není aplikované CSS které skrývá cenu
                const priceElement = document.querySelector('.product-price');
                if (priceElement) {
                    const priceStyle = window.getComputedStyle(priceElement);
                    if (priceStyle.display === 'none') {
                        console.log('PriceInquiry: Nalezeno CSS které skrývá cenu, odstraňuji...');
                        this.applyCssHiding(false);
                    }
                }

                // Ujistíme se, že je náš blok skrytý
                if (priceInquiryBlock.style.display !== 'none') {
                    priceInquiryBlock.style.display = 'none';
                    console.log('PriceInquiry: Blok skryt');
                }
            }
        },

        handleInquiryClick: function(button) {
            const data = button.dataset;
            const modal = document.getElementById('piModal');
            if (!modal) return;

            modal.querySelector('#piProductId').value = data.productId || '';
            modal.querySelector('#piProductNameHidden').value = data.productName || '';
            modal.querySelector('#piProductReferenceHidden').value = data.productReference || '';
            modal.querySelector('#piProductName').textContent = data.productName || '';
            modal.querySelector('#piProductReference').textContent = data.productReference || '';
            
            const img = modal.querySelector('#piProductImage');
            if (img) {
                img.src = data.productImage || '';
                img.alt = data.productName || '';
            }

            if (data.isLogged === '1') {
                modal.querySelector('#piCustomerName').value = data.customerName || '';
                modal.querySelector('#piCustomerEmail').value = data.customerEmail || '';
            }

            this.openModal();
        },

        openModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'block';
            document.body.classList.add('pi-modal-open');
        },

        closeModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'none';
            document.body.classList.remove('pi-modal-open');
        }
    };

    // Debug: Zkontrolujeme, jestli se JavaScript vůbec načítá
    console.log('PriceInquiry: JavaScript soubor načten!');

    // Vystavíme PriceInquiryJS globálně pro debugging
    window.PriceInquiryJS = PriceInquiryJS;

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('PriceInquiry: DOM loaded, initializing...');
            PriceInquiryJS.init();
            // Zkontrolujeme počáteční stav po krátké pauze
            setTimeout(() => {
                console.log('PriceInquiry: Kontroluji počáteční stav...');
                PriceInquiryJS.checkInitialState();
            }, 100);
        });
    } else {
        console.log('PriceInquiry: DOM ready, initializing...');
        PriceInquiryJS.init();
        // Zkontrolujeme počáteční stav po krátké pauze
        setTimeout(() => {
            console.log('PriceInquiry: Kontroluji počáteční stav...');
            PriceInquiryJS.checkInitialState();
        }, 100);
    }
})();