/**
 * JavaScript pro frontend modul "Cena na dotaz"
 * Verze 5.0.0 - Obsluhuje pouze modal.
 */
(function() {
    "use strict";

    const PriceInquiryJS = {
        init: function() {
            if (this.initialized) return;
            this.initialized = true;
            this.bindEvents();
        },

        bindEvents: function() {
            document.body.addEventListener('click', (e) => {
                const inquiryButton = e.target.closest('.pi-btn');
                if (inquiryButton) {
                    e.preventDefault();
                    this.handleInquiryClick(inquiryButton);
                    return;
                }

                const closeModalButton = e.target.closest('.pi-modal-close, .pi-modal-overlay');
                if (closeModalButton) {
                    e.preventDefault();
                    this.closeModal();
                }
            });
            
            document.body.addEventListener('submit', (e) => {
                if (e.target.id === 'piForm') {
                    e.preventDefault();
                    // Zde bude logika pro odeslání formuláře
                }
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && document.getElementById('piModal')?.style.display !== 'none') {
                    this.closeModal();
                }
            });
        },
        
        handleInquiryClick: function(button) {
            const data = button.dataset;
            const modal = document.getElementById('piModal');
            if (!modal) return;

            modal.querySelector('#piProductId').value = data.productId || '';
            modal.querySelector('#piProductNameHidden').value = data.productName || '';
            modal.querySelector('#piProductReferenceHidden').value = data.productReference || '';
            modal.querySelector('#piProductName').textContent = data.productName || '';
            modal.querySelector('#piProductReference').textContent = data.productReference || '';
            
            const img = modal.querySelector('#piProductImage');
            if (img) {
                img.src = data.productImage || '';
                img.alt = data.productName || '';
            }

            if (data.isLogged === '1') {
                modal.querySelector('#piCustomerName').value = data.customerName || '';
                modal.querySelector('#piCustomerEmail').value = data.customerEmail || '';
            }

            this.openModal();
        },

        openModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'block';
            document.body.classList.add('pi-modal-open');
        },

        closeModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'none';
            document.body.classList.remove('pi-modal-open');
        }
    };

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => PriceInquiryJS.init());
    } else {
        PriceInquiryJS.init();
    }
})();