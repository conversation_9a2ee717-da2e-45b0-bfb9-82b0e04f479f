/**
 * JavaScript pro frontend modul "Cena na dotaz"
 * Verze 6.0.0 - Obsluhuje modal a reaguje na změny variant produktu.
 */
(function() {
    "use strict";

    const PriceInquiryJS = {
        init: function() {
            if (this.initialized) return;
            this.initialized = true;
            this.bindEvents();
            this.bindPrestaShopEvents();
        },

        bindEvents: function() {
            document.body.addEventListener('click', (e) => {
                const inquiryButton = e.target.closest('.pi-btn');
                if (inquiryButton) {
                    e.preventDefault();
                    this.handleInquiryClick(inquiryButton);
                    return;
                }

                const closeModalButton = e.target.closest('.pi-modal-close, .pi-modal-overlay');
                if (closeModalButton) {
                    e.preventDefault();
                    this.closeModal();
                }
            });

            document.body.addEventListener('submit', (e) => {
                if (e.target.id === 'piForm') {
                    e.preventDefault();
                    // Zde bude logika pro odes<PERSON>ání formulář<PERSON>
                }
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && document.getElementById('piModal')?.style.display !== 'none') {
                    this.closeModal();
                }
            });
        },

        bindPrestaShopEvents: function() {
            // Reagujeme na změny variant produktu
            if (typeof prestashop !== 'undefined') {
                prestashop.on('updateProduct', (event) => {
                    console.log('PriceInquiry: updateProduct event');
                });

                prestashop.on('updatedProduct', (event) => {
                    console.log('PriceInquiry: updatedProduct event');
                    this.handleProductUpdate(event);
                });
            }
        },

        handleProductUpdate: function(event) {
            console.log('PriceInquiry: handleProductUpdate called', event);
            // Počkáme chvilku, až se DOM aktualizuje
            setTimeout(() => {
                this.checkAndUpdatePriceInquiry();
            }, 100);

            // Zkusíme to ještě jednou po delší době pro jistotu
            setTimeout(() => {
                this.checkAndUpdatePriceInquiry();
            }, 1000);
        },

        checkAndUpdatePriceInquiry: function() {
            console.log('PriceInquiry: Spouštím kontrolu ceny...');

            // Nejdříve zkontrolujeme, jestli existuje náš blok
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (!priceInquiryBlock) {
                console.log('PriceInquiry: Blok .price-inquiry-block-detail nenalezen - modul pravděpodobně není aktivní na této stránce');
                return;
            }

            // Zkontrolujeme počáteční cenu z data atributu
            const initialPrice = priceInquiryBlock.getAttribute('data-initial-price');
            console.log('PriceInquiry: Počáteční cena z PHP:', initialPrice);

            // Najdeme aktuální cenu produktu - zkusíme různé selektory
            const priceSelectors = [
                '.product-prices .price',
                '.current-price .price',
                '.product-price .price',
                '.price',
                '.current-price',
                '.product-prices',
                '[data-field="price"]'
            ];

            let priceElement = null;
            let priceText = '';

            for (const selector of priceSelectors) {
                const elements = document.querySelectorAll(selector);
                console.log('PriceInquiry: Hledám selector:', selector, 'nalezeno:', elements.length);

                for (const element of elements) {
                    const text = element.textContent || element.innerText || '';
                    if (text.trim() && text.includes('Kč')) {
                        priceElement = element;
                        priceText = text;
                        console.log('PriceInquiry: Nalezen cenový element:', selector, 'text:', priceText);
                        break;
                    }
                }
                if (priceElement) break;
            }

            if (!priceElement || !priceText.trim()) {
                console.log('PriceInquiry: Cenový element nenalezen, používám počáteční cenu');
                // Použijeme počáteční cenu z PHP
                const initialPriceValue = parseFloat(initialPrice) || 0;
                if (initialPriceValue <= 0) {
                    this.showPriceInquiry();
                } else {
                    this.hidePriceInquiry();
                }
                return;
            }

            // Získáme číselnou hodnotu z textu ceny
            const priceMatch = priceText.match(/[\d,\.]+/);
            let priceValue = 0;

            if (priceMatch) {
                // Nahradíme čárku tečkou pro správný parsing
                const cleanPrice = priceMatch[0].replace(',', '.');
                priceValue = parseFloat(cleanPrice);
            }

            console.log('PriceInquiry: Detekovaná cena:', priceValue, 'z textu:', priceText);

            // Kontrolujeme, zda je cena nulová
            const isZeroPrice = priceValue === 0 ||
                               priceText.includes('0,00') ||
                               priceText.includes('0.00') ||
                               priceText.trim() === '0';

            if (isZeroPrice) {
                // Cena je nulová - zobrazíme "cena na dotaz"
                this.showPriceInquiry();
            } else {
                // Cena není nulová - skryjeme "cena na dotaz"
                this.hidePriceInquiry();
            }
        },

        showPriceInquiry: function() {
            console.log('PriceInquiry: Zobrazuji "cena na dotaz"');

            // Přidáme CSS styly pro skrytí ceny a tlačítek
            let styleElement = document.getElementById('pi-dynamic-styles');
            if (!styleElement) {
                styleElement = document.createElement('style');
                styleElement.id = 'pi-dynamic-styles';
                document.head.appendChild(styleElement);
            }

            styleElement.textContent = `
                .product-prices { display: none !important; }
                .product-add-to-cart .product-quantity,
                .product-add-to-cart .add { display: none !important; }
            `;

            // Zobrazíme náš blok (pokud existuje)
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (priceInquiryBlock) {
                priceInquiryBlock.style.display = 'block';
                console.log('PriceInquiry: Blok zobrazen');
            } else {
                console.log('PriceInquiry: Blok nenalezen!');
            }
        },

        hidePriceInquiry: function() {
            console.log('PriceInquiry: Skrývám "cena na dotaz"');

            // Odstraníme CSS styly pro skrytí ceny a tlačítek
            const styleElement = document.getElementById('pi-dynamic-styles');
            if (styleElement) {
                styleElement.remove();
            }

            // Skryjeme náš blok
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (priceInquiryBlock) {
                priceInquiryBlock.style.display = 'none';
                console.log('PriceInquiry: Blok skryt');
            }
        },

        handleInquiryClick: function(button) {
            const data = button.dataset;
            const modal = document.getElementById('piModal');
            if (!modal) return;

            modal.querySelector('#piProductId').value = data.productId || '';
            modal.querySelector('#piProductNameHidden').value = data.productName || '';
            modal.querySelector('#piProductReferenceHidden').value = data.productReference || '';
            modal.querySelector('#piProductName').textContent = data.productName || '';
            modal.querySelector('#piProductReference').textContent = data.productReference || '';
            
            const img = modal.querySelector('#piProductImage');
            if (img) {
                img.src = data.productImage || '';
                img.alt = data.productName || '';
            }

            if (data.isLogged === '1') {
                modal.querySelector('#piCustomerName').value = data.customerName || '';
                modal.querySelector('#piCustomerEmail').value = data.customerEmail || '';
            }

            this.openModal();
        },

        openModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'block';
            document.body.classList.add('pi-modal-open');
        },

        closeModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'none';
            document.body.classList.remove('pi-modal-open');
        }
    };

    // Debug: Zkontrolujeme, jestli se JavaScript vůbec načítá
    console.log('PriceInquiry: JavaScript soubor načten!');

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('PriceInquiry: DOM loaded, initializing...');
            PriceInquiryJS.init();
            // Zkontrolujeme stav při načtení stránky
            setTimeout(() => {
                console.log('PriceInquiry: Running initial check...');
                PriceInquiryJS.checkAndUpdatePriceInquiry();
            }, 500);
        });
    } else {
        console.log('PriceInquiry: DOM ready, initializing...');
        PriceInquiryJS.init();
        // Zkontrolujeme stav při načtení stránky
        setTimeout(() => {
            console.log('PriceInquiry: Running initial check...');
            PriceInquiryJS.checkAndUpdatePriceInquiry();
        }, 500);
    }
})();