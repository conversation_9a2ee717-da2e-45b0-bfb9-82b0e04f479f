/**
 * JavaScript pro frontend modul "Cena na dotaz"
 * Verze 6.0.0 - Obsluhuje modal a reaguje na změny variant produktu.
 */
(function() {
    "use strict";

    const PriceInquiryJS = {
        init: function() {
            if (this.initialized) return;
            this.initialized = true;
            this.bindEvents();
            this.bindPrestaShopEvents();
        },

        bindEvents: function() {
            document.body.addEventListener('click', (e) => {
                const inquiryButton = e.target.closest('.pi-btn');
                if (inquiryButton) {
                    e.preventDefault();
                    this.handleInquiryClick(inquiryButton);
                    return;
                }

                const closeModalButton = e.target.closest('.pi-modal-close, .pi-modal-overlay');
                if (closeModalButton) {
                    e.preventDefault();
                    this.closeModal();
                }
            });

            document.body.addEventListener('submit', (e) => {
                if (e.target.id === 'piForm') {
                    e.preventDefault();
                    // Zde bude logika pro odes<PERSON>ání formulář<PERSON>
                }
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && document.getElementById('piModal')?.style.display !== 'none') {
                    this.closeModal();
                }
            });
        },

        bindPrestaShopEvents: function() {
            // Reagujeme na změny variant produktu
            if (typeof prestashop !== 'undefined') {
                prestashop.on('updateProduct', (event) => {
                    console.log('PriceInquiry: updateProduct event');
                });

                prestashop.on('updatedProduct', (event) => {
                    console.log('PriceInquiry: updatedProduct event');
                    this.handleProductUpdate(event);
                });
            }
        },

        handleProductUpdate: function(event) {
            // Počkáme chvilku, až se DOM aktualizuje
            setTimeout(() => {
                this.checkAndUpdatePriceInquiry();
            }, 100);
        },

        checkAndUpdatePriceInquiry: function() {
            // Najdeme aktuální cenu produktu - zkusíme různé selektory
            const priceSelectors = [
                '.product-prices .price',
                '.current-price .price',
                '.product-price .price',
                '.price',
                '.current-price',
                '.product-prices',
                '[data-field="price"]'
            ];

            let priceElement = null;
            let priceText = '';

            for (const selector of priceSelectors) {
                priceElement = document.querySelector(selector);
                if (priceElement) {
                    priceText = priceElement.textContent || priceElement.innerText || '';
                    if (priceText.trim()) {
                        console.log('PriceInquiry: Nalezen cenový element:', selector, 'text:', priceText);
                        break;
                    }
                }
            }

            if (!priceElement || !priceText.trim()) {
                console.log('PriceInquiry: Cenový element nenalezen');
                return;
            }

            // Získáme číselnou hodnotu z textu ceny
            const priceMatch = priceText.match(/[\d,\.]+/);
            let priceValue = 0;

            if (priceMatch) {
                // Nahradíme čárku tečkou pro správný parsing
                const cleanPrice = priceMatch[0].replace(',', '.');
                priceValue = parseFloat(cleanPrice);
            }

            console.log('PriceInquiry: Detekovaná cena:', priceValue, 'z textu:', priceText);

            // Kontrolujeme, zda je cena nulová
            const isZeroPrice = priceValue === 0 ||
                               priceText.includes('0,00') ||
                               priceText.includes('0.00') ||
                               priceText.trim() === '0';

            if (isZeroPrice) {
                // Cena je nulová - zobrazíme "cena na dotaz"
                this.showPriceInquiry();
            } else {
                // Cena není nulová - skryjeme "cena na dotaz"
                this.hidePriceInquiry();
            }
        },

        showPriceInquiry: function() {
            console.log('PriceInquiry: Zobrazuji "cena na dotaz"');

            // Skryjeme standardní cenu a tlačítka - zkusíme různé selektory
            const priceSelectors = ['.product-prices', '.current-price', '.product-price'];
            const quantitySelectors = ['.product-add-to-cart .product-quantity', '.product-quantity', '.qty'];
            const buttonSelectors = ['.product-add-to-cart .add', '.add-to-cart', '.btn-primary'];

            priceSelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.display = 'none';
                }
            });

            quantitySelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.display = 'none';
                }
            });

            buttonSelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element && element.textContent &&
                    (element.textContent.includes('košík') ||
                     element.textContent.includes('cart') ||
                     element.textContent.includes('Přidat'))) {
                    element.style.display = 'none';
                }
            });

            // Zobrazíme náš blok (pokud existuje)
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (priceInquiryBlock) {
                priceInquiryBlock.style.display = 'block';
            }
        },

        hidePriceInquiry: function() {
            console.log('PriceInquiry: Skrývám "cena na dotaz"');

            // Zobrazíme standardní cenu a tlačítka - zkusíme různé selektory
            const priceSelectors = ['.product-prices', '.current-price', '.product-price'];
            const quantitySelectors = ['.product-add-to-cart .product-quantity', '.product-quantity', '.qty'];
            const buttonSelectors = ['.product-add-to-cart .add', '.add-to-cart', '.btn-primary'];

            priceSelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.display = '';
                }
            });

            quantitySelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.display = '';
                }
            });

            buttonSelectors.forEach(selector => {
                const element = document.querySelector(selector);
                if (element && element.textContent &&
                    (element.textContent.includes('košík') ||
                     element.textContent.includes('cart') ||
                     element.textContent.includes('Přidat'))) {
                    element.style.display = '';
                }
            });

            // Skryjeme náš blok
            const priceInquiryBlock = document.querySelector('.price-inquiry-block-detail');
            if (priceInquiryBlock) {
                priceInquiryBlock.style.display = 'none';
            }
        },

        handleInquiryClick: function(button) {
            const data = button.dataset;
            const modal = document.getElementById('piModal');
            if (!modal) return;

            modal.querySelector('#piProductId').value = data.productId || '';
            modal.querySelector('#piProductNameHidden').value = data.productName || '';
            modal.querySelector('#piProductReferenceHidden').value = data.productReference || '';
            modal.querySelector('#piProductName').textContent = data.productName || '';
            modal.querySelector('#piProductReference').textContent = data.productReference || '';
            
            const img = modal.querySelector('#piProductImage');
            if (img) {
                img.src = data.productImage || '';
                img.alt = data.productName || '';
            }

            if (data.isLogged === '1') {
                modal.querySelector('#piCustomerName').value = data.customerName || '';
                modal.querySelector('#piCustomerEmail').value = data.customerEmail || '';
            }

            this.openModal();
        },

        openModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'block';
            document.body.classList.add('pi-modal-open');
        },

        closeModal: function() {
            const modal = document.getElementById('piModal');
            if (modal) modal.style.display = 'none';
            document.body.classList.remove('pi-modal-open');
        }
    };

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            PriceInquiryJS.init();
            // Zkontrolujeme stav při načtení stránky
            setTimeout(() => PriceInquiryJS.checkAndUpdatePriceInquiry(), 500);
        });
    } else {
        PriceInquiryJS.init();
        // Zkontrolujeme stav při načtení stránky
        setTimeout(() => PriceInquiryJS.checkAndUpdatePriceInquiry(), 500);
    }
})();