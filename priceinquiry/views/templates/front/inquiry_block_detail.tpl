{*
* Blok pro detail produktu, v<PERSON><PERSON><PERSON><PERSON><PERSON> do `displayProductActions`.
*}

{* Blok se defaultně sk<PERSON>, JavaScript ho zobrazí podle potřeby *}
<div class="price-inquiry-block-detail" style="display: none;">
    <div class="pi-price-text-wrapper">
        <span class="pi-price-text">
            {l s='Cena na dotaz' mod='priceinquiry'}
        </span>
    </div>

    <div class="pi-container-simple">
        <button type="button"
                class="btn btn-primary pi-btn"
                data-product-id="{$product_id}"
                data-product-name="{$product_name|escape:'html':'UTF-8'}"
                data-product-reference="{$product_reference|escape:'html':'UTF-8'}"
                data-product-image="{$product_image|escape:'html':'UTF-8'}"
                data-is-logged="{if $is_logged}1{else}0{/if}"
                {if $is_logged}
                data-customer-name="{$customer_name|escape:'html':'UTF-8'}"
                data-customer-email="{$customer_email|escape:'html':'UTF-8'}"
                {/if}>
            <i class="material-icons">contact_mail</i>
            {l s='Zjistit cenu' mod='priceinquiry'}
        </button>
    </div>
</div>