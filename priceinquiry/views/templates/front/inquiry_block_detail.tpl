{*
* Blok pro detail produktu, v<PERSON><PERSON><PERSON><PERSON><PERSON> do `displayProductActions`.
*}

<style>
    /* Skryjeme cenu */
    .product-prices {
        display: none !important;
    }
    /* Najdeme rodiče a v něm skryjeme jen to, co je potřeba */
    .product-add-to-cart .product-quantity,
    .product-add-to-cart .add {
        display: none !important;
    }
    /* <PERSON><PERSON><PERSON> blok se zobrazí jako první */
    .price-inquiry-block-detail {
        order: -1; /* Zobrazí se před ostatními flex položkami, jako je wishlist */
        width: 100%;
        margin-bottom: 15px;
    }
</style>

<div class="price-inquiry-block-detail">
    <div class="pi-price-text-wrapper">
        <span class="pi-price-text">
            {l s='Cena na dotaz' mod='priceinquiry'}
        </span>
    </div>

    <div class="pi-container-simple">
        <button type="button"
                class="btn btn-primary pi-btn"
                data-product-id="{$product_id}"
                data-product-name="{$product_name|escape:'html':'UTF-8'}"
                data-product-reference="{$product_reference|escape:'html':'UTF-8'}"
                data-product-image="{$product_image|escape:'html':'UTF-8'}"
                data-is-logged="{if $is_logged}1{else}0{/if}"
                {if $is_logged}
                data-customer-name="{$customer_name|escape:'html':'UTF-8'}"
                data-customer-email="{$customer_email|escape:'html':'UTF-8'}"
                {/if}>
            <i class="material-icons">contact_mail</i>
            {l s='Zjistit cenu' mod='priceinquiry'}
        </button>
    </div>
</div>