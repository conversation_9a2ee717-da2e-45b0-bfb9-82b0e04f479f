{*
* Blok pro detail produktu, v<PERSON><PERSON><PERSON><PERSON><PERSON> do `displayProductActions`.
*}

{* Debug informace *}
<!-- PriceInquiry Debug: show_block={$show_block}, current_price={$current_price} -->

{* CSS pro skrytí ceny a tlačítek když je cena nulová *}
{if $current_price <= 0}
<style>
    /* Skryjeme cenu */
    .product-prices {
        display: none !important;
    }
    /* Najdeme rodiče a v něm skryjeme jen to, co je potřeba */
    .product-add-to-cart .product-quantity,
    .product-add-to-cart .add {
        display: none !important;
    }
</style>
{/if}

<div class="price-inquiry-block-detail" style="display: {if $current_price <= 0}block{else}none{/if};" data-initial-price="{$current_price}">
    <div class="pi-price-text-wrapper">
        <span class="pi-price-text">
            {l s='Cena na dotaz' mod='priceinquiry'}
        </span>
    </div>

    <div class="pi-container-simple">
        <button type="button"
                class="btn btn-primary pi-btn"
                data-product-id="{$product_id}"
                data-product-name="{$product_name|escape:'html':'UTF-8'}"
                data-product-reference="{$product_reference|escape:'html':'UTF-8'}"
                data-product-image="{$product_image|escape:'html':'UTF-8'}"
                data-is-logged="{if $is_logged}1{else}0{/if}"
                {if $is_logged}
                data-customer-name="{$customer_name|escape:'html':'UTF-8'}"
                data-customer-email="{$customer_email|escape:'html':'UTF-8'}"
                {/if}>
            <i class="material-icons">contact_mail</i>
            {l s='Zjistit cenu' mod='priceinquiry'}
        </button>
    </div>
</div>