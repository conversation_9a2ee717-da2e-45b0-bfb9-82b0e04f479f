/**
 * Test skript pro spuštění přímo na stránce produktu
 * Vložte do konzole prohlížeče na stránce produktu
 */

console.log('=== PriceInquiry Test na stránce produktu ===');

// 1. Test existence objektů
console.log('1. Test existence objektů:');
console.log('  PriceInquiryJS:', typeof PriceInquiryJS !== 'undefined' ? '✓ EXISTS' : '✗ MISSING');
console.log('  prestashop:', typeof prestashop !== 'undefined' ? '✓ EXISTS' : '✗ MISSING');

// 2. Test našich bloků
console.log('2. Test našich bloků:');
const piBlocks = document.querySelectorAll('.price-inquiry-block-detail');
console.log('  Nalezeno bloků:', piBlocks.length);

piBlocks.forEach((block, index) => {
    const isVisible = window.getComputedStyle(block).display !== 'none';
    const initialPrice = block.getAttribute('data-initial-price');
    console.log(`  Blok ${index}:`, {
        visible: isVisible,
        initialPrice: initialPrice,
        position: block.getBoundingClientRect()
    });
});

// 3. Test cenových elementů
console.log('3. Test cenových elementů:');
const priceSelectors = [
    '.product-prices .price',
    '.current-price .price', 
    '.product-price .price',
    '.price'
];

priceSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`  ${selector}:`, elements.length, 'elementů');
        elements.forEach((el, i) => {
            const text = el.textContent.trim();
            if (text && text.includes('Kč')) {
                console.log(`    [${i}]:`, text);
            }
        });
    }
});

// 4. Test funkčnosti
if (typeof PriceInquiryJS !== 'undefined') {
    console.log('4. Test funkčnosti:');
    
    // Test detekce ceny
    console.log('  Spouštím checkAndUpdatePriceInquiry...');
    PriceInquiryJS.checkAndUpdatePriceInquiry();
    
    // Test event systému
    if (typeof prestashop !== 'undefined') {
        console.log('  Testuju event systém...');
        prestashop.emit('updatedProduct', { test: true });
    }
}

// 5. Monitoring změn
console.log('5. Spouštím monitoring změn...');

let changeCount = 0;
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' || mutation.type === 'attributes') {
            changeCount++;
            console.log(`Změna ${changeCount}:`, mutation.type, mutation.target);
            
            // Zkontrolujeme naše bloky po změně
            setTimeout(() => {
                const blocks = document.querySelectorAll('.price-inquiry-block-detail');
                const visible = Array.from(blocks).filter(b => 
                    window.getComputedStyle(b).display !== 'none'
                ).length;
                console.log(`  Po změně: ${blocks.length} bloků, ${visible} viditelných`);
            }, 100);
        }
    });
});

// Sledujeme změny v celém dokumentu
observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['style', 'class']
});

console.log('=== Test dokončen - sledování pokračuje ===');
console.log('Pro zastavení monitoring: observer.disconnect()');

// Funkce pro manuální testy
window.testPriceInquiry = {
    showBlock: function() {
        if (typeof PriceInquiryJS !== 'undefined') {
            PriceInquiryJS.showPriceInquiry();
            console.log('showPriceInquiry() zavolána');
        }
    },
    
    hideBlock: function() {
        if (typeof PriceInquiryJS !== 'undefined') {
            PriceInquiryJS.hidePriceInquiry();
            console.log('hidePriceInquiry() zavolána');
        }
    },
    
    checkPrice: function() {
        if (typeof PriceInquiryJS !== 'undefined') {
            PriceInquiryJS.checkAndUpdatePriceInquiry();
            console.log('checkAndUpdatePriceInquiry() zavolána');
        }
    },
    
    simulateVariantChange: function() {
        if (typeof prestashop !== 'undefined') {
            prestashop.emit('updatedProduct', { reason: 'manual_test' });
            console.log('updatedProduct event emitován');
        }
    }
};

console.log('Dostupné manuální testy:');
console.log('  testPriceInquiry.showBlock()');
console.log('  testPriceInquiry.hideBlock()');
console.log('  testPriceInquiry.checkPrice()');
console.log('  testPriceInquiry.simulateVariantChange()');
