<?php
/**
 * <PERSON><PERSON><PERSON> "Cena na dotaz" pro PrestaShop
 * Verze 7.1.0 - P<PERSON><PERSON><PERSON><PERSON> podpora pro dynamické změny variant produktu
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class PriceInquiry extends Module
{
    public function __construct()
    {
        $this->name = 'priceinquiry';
        $this->tab = 'front_office_features';
        $this->version = '7.1.0';
        $this->author = '<PERSON><PERSON><PERSON> (Opraveno Gemini)';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = ['min' => '1.7', 'max' => _PS_VERSION_];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Cena na dotaz');
        $this->description = $this->l('Zobrazuje "Cena na dotaz" místo ceny 0 Kč a umožňuje zákazníkům poslat dotaz na cenu.');
    }

    public function install()
    {
        return parent::install() &&
            $this->registerHook('displayProductPriceBlock') && // Pro přehled produktů
            $this->registerHook('displayProductActions') &&    // Pro detail produktu
            $this->registerHook('displayHeader') &&
            $this->registerHook('displayFooter') &&
            Configuration::updateValue('PRICE_INQUIRY_ENABLED', 1);
    }

    public function uninstall()
    {
        return parent::uninstall();
    }

    public function hookDisplayHeader()
    {
        $this->context->controller->addCSS($this->_path.'views/css/front.css');
        $this->context->controller->addJS($this->_path.'views/js/front.js');
    }

    /**
     * Hook pro PŘEHLED PRODUKTŮ (kategorie).
     */
    public function hookDisplayProductPriceBlock($params)
    {
        if ($this->context->controller->php_self === 'product' || !Configuration::get('PRICE_INQUIRY_ENABLED')) {
            return '';
        }
        if ($params['type'] !== 'before_price' && $params['type'] !== 'unit_price') {
            return '';
        }
        $product_id = (int)($params['product']['id_product'] ?? $params['product']['id'] ?? 0);
        if (!$product_id) {
            return '';
        }
        $price = Product::getPriceStatic($product_id, true, null, 2, null, false, true);
        if ($price > 0) {
            return '';
        }
        $price_text = Configuration::get('PRICE_INQUIRY_PRICE_TEXT') ?: $this->l('Cena na dotaz');
        return '<div class="pi-category-text" data-id-product="'.$product_id.'">
                    <style>
                        .product-miniature[data-id-product="'.$product_id.'"] .product-price-and-shipping { display: none !important; }
                    </style>
                    '.$price_text.'
                </div>';
    }

    /**
     * Hook pro DETAIL PRODUKTU.
     * Verze 7.1.0 - Kontroluje cenu při načtení, JavaScript pak reaguje na změny variant
     */
    public function hookDisplayProductActions($params)
    {
        error_log("PriceInquiry: hookDisplayProductActions called");

        if ($this->context->controller->php_self !== 'product' || !Configuration::get('PRICE_INQUIRY_ENABLED')) {
            error_log("PriceInquiry: Hook skipped - not product page or not enabled");
            return '';
        }

        $product = $this->context->controller->getProduct();
        if (!Validate::isLoadedObject($product)) {
            return '';
        }

        $id_product_attribute = (int)Tools::getValue('id_product_attribute', Product::getDefaultAttribute($product->id));
        $price = Product::getPriceStatic($product->id, true, $id_product_attribute);

        // Debug informace
        error_log("PriceInquiry Debug: Product ID: {$product->id}, Attribute ID: {$id_product_attribute}, Price: {$price}");

        // Zobrazujeme blok vždy - JavaScript pak bude reagovat na změny variant
        // Ale při načtení stránky kontrolujeme cenu pro správné zobrazení
        $showBlock = true; // Vždy zobrazíme blok, JavaScript ho pak skryje/zobrazí podle potřeby

        $image = Image::getCover($product->id);
        $productImage = $this->context->link->getImageLink($product->link_rewrite, $image['id_image'] ?? null, 'home_default');

        $this->context->smarty->assign([
            'product_id' => $product->id,
            'product_name' => $product->name,
            'product_reference' => $product->reference,
            'product_image' => $productImage,
            'is_logged' => $this->context->customer->isLogged(),
            'customer_name' => $this->context->customer->isLogged() ? $this->context->customer->firstname.' '.$this->context->customer->lastname : '',
            'customer_email' => $this->context->customer->isLogged() ? $this->context->customer->email : '',
            'show_block' => $showBlock,
            'current_price' => $price
        ]);

        return $this->display(__FILE__, 'views/templates/front/inquiry_block_detail.tpl');
    }

    public function hookDisplayFooter($params)
    {
        if (!Configuration::get('PRICE_INQUIRY_ENABLED')) return '';
        $this->context->smarty->assign(['link' => $this->context->link]);
        return $this->display(__FILE__, 'views/templates/front/inquiry_modal.tpl');
    }
    
    public function getContent()
    {
        return "Module configuration";
    }
}
