> // Načti test skript
fetch('/modules/priceinquiry/test_on_product.js')
  .then(r => r.text())
  .then(code => eval(code));
< Promise {status: "pending"}
[Log] === PriceInquiry Test na stránce produktu ===
[Log] 1. Test existence objektů:
[Log]   PriceInquiryJS: – "✓ EXISTS"
[Log]   prestashop: – "✓ EXISTS"
[Log] 2. Test našich bloků:
[Log]   Na<PERSON><PERSON><PERSON> bloků: – 1
[Log]   Blok 0: – {visible: true, initialPrice: "0", position: DOMRect}
{visible: true, initialPrice: "0", position: DOMRect}Object
[Log] 3. Test cenových elementů:
[Log]   .price: – 8 – "elementů"
[Log]     [0]: – "9,40 Kč"
[Log]     [1]: – "9,70 Kč"
[Log]     [2]: – "44,30 Kč"
[Log]     [3]: – "10,40 Kč"
[Log]     [4]: – "17,20 Kč"
[Log]     [5]: – "102,00 Kč"
[Log]     [6]: – "24,30 Kč"
[Log]     [7]: – "38,60 Kč"
[Log] 4. Test funkčnosti:
[Log]   Spouštím checkAndUpdatePriceInquiry...
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log]   Testuju event systém...
[Log] PriceInquiry: updatedProduct event (front.js, line 54)
[Log] PriceInquiry: handleProductUpdate called – {test: true} (front.js, line 61)
[Log] 5. Spouštím monitoring změn...
[Log] === Test dokončen - sledování pokračuje ===
[Log] Pro zastavení monitoring: observer.disconnect()
[Log] Dostupné manuální testy:
[Log]   testPriceInquiry.showBlock()
[Log]   testPriceInquiry.hideBlock()
[Log]   testPriceInquiry.checkPrice()
[Log]   testPriceInquiry.simulateVariantChange()
[Log] Změna 1: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000858%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000858%; overflow: hidden;"></div>
[Log] Změna 2: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 3: – "childList" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 4: – "childList" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 5: – "childList" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 6: – "childList" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 7: – "childList" – 
<div class="zoomWindowContainer" style="width: 350px;">…</div>

<div class="zoomWindowContainer" style="width: 350px;">…</div>
[Log] Změna 8: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 9: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000579%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000579%; overflow: hidden;"></div>
[Log] Změna 10: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000346%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000346%; overflow: hidden;"></div>
[Log] Změna 11: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.00015%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.00015%; overflow: hidden;"></div>
[Log] Změna 12: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000022%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000022%; overflow: hidden;"></div>
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log] Změna 13: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 14: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x14)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log] Změna 15: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 16: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 17: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 18: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 19: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 20: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 21: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 22: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 23: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 24: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 25: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 26: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 27: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 28: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 29: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 30: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 31: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 32: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 33: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 34: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 35: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 36: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 37: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 38: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 39: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 40: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 41: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 42: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 43: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden;"></div>
[Log] Změna 44: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>
[Log]   Po změně: 1 bloků, 1 viditelných (x30)
[Log] Změna 45: – "attributes" – 
<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 352.757324px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 46: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 47: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 48: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 49: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 50: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 51: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 52: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 53: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 54: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 55: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 56: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 57: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 58: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 59: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 60: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 61: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 62: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 63: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 64: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 65: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 66: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 67: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 68: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 69: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 70: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 71: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 72: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 73: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 74: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 75: – "attributes" – 
<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>

<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>
[Log]   Po změně: 1 bloků, 1 viditelných (x31)
[Log] Změna 76: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 77: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 78: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 79: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -478.255564px -31.303698px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 80: – "attributes" – 
<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>

<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>
[Log] Změna 81: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 82: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 242px; top: 15px;"></div>
[Log] Změna 83: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -556.993446px -379.721172px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -556.993446px -379.721172px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 84: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -556.993446px -379.721172px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -556.993446px -379.721172px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px;"></div>
[Log] Změna 85: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>
[Log] Změna 86: – "attributes" – 
<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 282px; top: 192px;"></div>

<div class="zoomLens" style="display: none; position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 282px; top: 192px;"></div>
[Log] Změna 87: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -405.311156px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: none;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -405.311156px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: none;"></div>
[Log] Změna 88: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 89: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 90: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 91: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 92: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 93: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 94: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 95: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -405.311156px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: none;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -405.311156px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: none;"></div>
[Log] Změna 96: – "attributes" – 
<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>

<div class="ZoomContainer" uuid="0e3769c8-f0b4-42f3-af63-5f94814369f7" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>
[Log] Změna 97: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 98: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 99: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 100: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 101: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 102: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091888px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 103: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 104: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 105: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 106: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 107: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -310.825061px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 108: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 157px;"></div>
[Log] Změna 109: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.589367; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.589367; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 110: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.010633; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 130px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.010633; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 130px;"></div>
[Log] Změna 111: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -257.676633px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -257.676633px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 112: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.010633; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 130px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.010633; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 130px;"></div>
[Log] Změna 113: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.577164; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.577164; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 114: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.022836; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 113px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.022836; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 113px;"></div>
[Log] Změna 115: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -224.212808px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -224.212808px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 116: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.022836; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 113px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.022836; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 113px;"></div>
[Log] Změna 117: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.559414; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.559414; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 118: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.040586; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 100px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.040586; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 100px;"></div>
[Log] Změna 119: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -198.622824px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -198.622824px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 120: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.040586; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 100px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.040586; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 100px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x11)
[Log] Změna 121: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.538483; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.538483; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 122: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.061517; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 85px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.061517; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 85px;"></div>
[Log] Změna 123: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -169.09592px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -169.09592px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 124: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.061517; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 85px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.061517; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 85px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x8)
[Log] Změna 125: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.512132; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.512132; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 126: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.087868; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 70px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.087868; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 70px;"></div>
[Log] Změna 127: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -139.569015px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -139.569015px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 128: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.087868; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 70px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.087868; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 70px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x14)
[Log] Změna 129: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.482005; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.482005; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 130: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.117995; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 55px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.117995; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 55px;"></div>
[Log] Změna 131: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -110.042111px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -110.042111px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 132: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.117995; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 55px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.117995; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 55px;"></div>
[Log] Změna 133: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.45068; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.45068; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 134: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.14932; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.14932; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 135: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -76.578285px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -76.578285px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 136: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.14932; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.14932; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 137: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.414805; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.414805; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 138: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.185195; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.185195; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 139: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.376887; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.376887; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 140: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.223113; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.223113; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 141: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.339936; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.339936; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 142: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.260064; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.260064; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 143: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.297644; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.297644; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 144: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.302356; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.302356; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 145: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.260064; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.260064; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 146: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.339936; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.339936; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 147: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.223113; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.223113; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 148: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.376887; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.376887; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 149: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.185195; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.185195; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 150: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.414805; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.414805; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 151: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.14932; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.14932; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 152: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.45068; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.45068; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 38px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 153: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.117995; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.117995; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 154: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.482005; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.482005; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 155: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.621307px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.621307px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 156: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -148.369176px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -148.369176px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 157: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.482005; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.482005; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>
[Log] Změna 158: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.086208; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.086208; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 159: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.513792; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.513792; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 160: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.061517; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.061517; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 161: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.538483; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.538483; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 162: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.040586; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.040586; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 163: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.559414; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.559414; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 75px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 164: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.022836; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.022836; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 165: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.577164; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.577164; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 166: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091919px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091919px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 167: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -354.131128px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -354.131128px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 168: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.577164; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.577164; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>
[Log] Změna 169: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.01002; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.01002; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 170: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.58998; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.58998; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 171: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 172: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 173: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.002524;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.002524;">…</a>
[Log] Změna 174: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.001812; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.001812; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px;"></div>
[Log] Změna 175: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.598188; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.598188; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>
[Log] Změna 176: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.002524;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.002524;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x5)
[Log] Změna 177: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px; display: none;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px; display: none;"></div>
[Log] Změna 178: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px; display: none;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px; display: none;"></div>
[Log] Změna 179: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px; display: none;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 205px; display: none;"></div>
[Log] Změna 180: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>
[Log] Změna 181: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.002524;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.002524;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 182: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.02351;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.02351;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 183: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091919px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.091919px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 184: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -354.131128px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -354.131128px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: block;"></div>
[Log] Změna 185: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 179px;"></div>
[Log] Změna 186: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.02351;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.02351;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 187: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.042379;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.042379;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x5)
[Log] Změna 188: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.066465;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.066465;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 189: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.095492;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.095492;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x6)
[Log] Změna 190: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.127029;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.127029;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x5)
[Log] Změna 191: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.162334;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.162334;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 192: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.206107;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.206107;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných
[Log] Změna 193: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.295675;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.295675;">…</a>
[Log]   Po změně: 1 bloků, 1 viditelných
[Log] Změna 194: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -643.263272px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: none;"></div>

<div class="zoomWindow" style="z-index: 100; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: -653.529412px -643.263272px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg"); top: 0px; left: 508.014679px; display: none;"></div>
[Log] Změna 195: – "attributes" – 
<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.209534px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>

<div class="ZoomContainer" uuid="bcd63b5b-d372-4b99-b3ba-f06ef4807f73" style="position: absolute; top: 363.209534px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999; display: none;">…</div>
[Log] Změna 196: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.345492;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.345492;">…</a>
[Log] Změna 197: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.590575; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.590575; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných
[Log] Změna 198: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.345492;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.345492;">…</a>
[Log] Změna 199: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.590575; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.590575; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných
[Log] Změna 200: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.393996;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.393996;">…</a>
[Log] Změna 201: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.578933; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.578933; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 202: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.5;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.5;">…</a>
[Log] Změna 203: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.539905; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.539905; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log] Změna 204: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.5;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.5;">…</a>
[Log] Změna 205: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.539905; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.539905; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných
[Log] Změna 206: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.602931;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.602931;">…</a>
[Log] Změna 207: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.485728; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.485728; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x3)
[Log] Změna 208: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.602931;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.602931;">…</a>
[Log] Změna 209: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.485728; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.485728; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 210: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.654508;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.654508;">…</a>
[Log] Změna 211: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.452712; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.452712; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 212: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.749093;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.749093;">…</a>
[Log] Změna 213: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.381432; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.381432; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 214: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.796428;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.796428;">…</a>
[Log] Změna 215: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.339936; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.339936; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 216: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.835343;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.835343;">…</a>
[Log] Změna 217: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.302356; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.302356; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 218: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.835343;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.835343;">…</a>
[Log] Změna 219: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.302356; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.302356; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 220: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.870871;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.870871;">…</a>
[Log] Změna 221: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.264739; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.264739; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 222: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.904508;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.904508;">…</a>
[Log] Změna 223: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.225393; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.225393; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 224: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.956346;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.956346;">…</a>
[Log] Změna 225: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.153414; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.153414; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 226: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.975528;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.975528;">…</a>
[Log] Změna 227: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.119874; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.119874; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 228: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.975528;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.975528;">…</a>
[Log] Změna 229: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.119874; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.119874; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 230: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.988634;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.988634;">…</a>
[Log] Změna 231: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.091226; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.091226; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 232: – "attributes" – 
<a class="top_button" href="#" style="display: inline; opacity: 0.988634;">…</a>

<a class="top_button" href="#" style="display: inline; opacity: 0.988634;">…</a>
[Log] Změna 233: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.091226; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.091226; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x4)
[Log] Změna 234: – "attributes" – 
<a class="top_button" href="#" style="display: inline;">…</a>

<a class="top_button" href="#" style="display: inline;">…</a>
[Log] Změna 235: – "attributes" – 
<a class="top_button" href="#" style="display: inline;">…</a>

<a class="top_button" href="#" style="display: inline;">…</a>
[Log] Změna 236: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.041777; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.041777; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log] Změna 237: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.011263; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.011263; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 238: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.011263; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.011263; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 239: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.002993; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.002993; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 240: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x2)
[Log] Změna 241: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>
[Log] Změna 242: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>
[Log] Změna 243: – "attributes" – 
<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>

<div class="zoomLens" style="position: absolute; height: 177.803941px; width: 177.805138px; border: 2px solid rgb(102, 102, 102); background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); opacity: 0.6; cursor: crosshair; z-index: 999; overflow: hidden; left: 329.749978px; top: 326px; display: none;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x12)
> // Načti test skript
fetch('/modules/priceinquiry/test_on_product.js')
  .then(r => r.text())
  .then(code => eval(code));
< Promise {status: "pending"}
[Log] === PriceInquiry Test na stránce produktu ===
[Log] 1. Test existence objektů:
[Log]   PriceInquiryJS: – "✓ EXISTS"
[Log]   prestashop: – "✓ EXISTS"
[Log] 2. Test našich bloků:
[Log]   Nalezeno bloků: – 1
[Log]   Blok 0: – {visible: true, initialPrice: "0", position: DOMRect}
{visible: true, initialPrice: "0", position: DOMRect}Object
[Log] 3. Test cenových elementů:
[Log]   .price: – 8 – "elementů"
[Log]     [0]: – "9,40 Kč"
[Log]     [1]: – "9,70 Kč"
[Log]     [2]: – "44,30 Kč"
[Log]     [3]: – "10,40 Kč"
[Log]     [4]: – "17,20 Kč"
[Log]     [5]: – "102,00 Kč"
[Log]     [6]: – "24,30 Kč"
[Log]     [7]: – "38,60 Kč"
[Log] 4. Test funkčnosti:
[Log]   Spouštím checkAndUpdatePriceInquiry...
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log]   Testuju event systém...
[Log] PriceInquiry: updatedProduct event (front.js, line 54)
[Log] PriceInquiry: handleProductUpdate called – {test: true} (front.js, line 61)
[Log] 5. Spouštím monitoring změn...
[Log] === Test dokončen - sledování pokračuje ===
[Log] Pro zastavení monitoring: observer.disconnect()
[Log] Dostupné manuální testy:
[Log]   testPriceInquiry.showBlock()
[Log]   testPriceInquiry.hideBlock()
[Log]   testPriceInquiry.checkPrice()
[Log]   testPriceInquiry.simulateVariantChange()
[Log] Změna 244: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 245: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 246: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 247: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 248: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 249: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 250: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 251: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 252: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 253: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 254: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 255: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 256: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 257: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 258: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 259: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 260: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 261: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 262: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 263: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 264: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 265: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 266: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 267: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 1: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 268: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 269: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 270: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 271: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 272: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 273: – "childList" – 
<div class="zoomWindowContainer" style="width: 350px;">…</div>

<div class="zoomWindowContainer" style="width: 350px;">…</div>
[Log] Změna 274: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 2: – "childList" – 
<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>

<body id="product" class="lang-cs country-cz currency-czk layout-full-width page-product tax-display-enabled product-id-9292 product-4-0-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu product-id-category-87 product-id-manufacturer-0 product-id-supplier-0 product-available-for-order">…</body>
[Log] Změna 3: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 4: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 5: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 6: – "childList" – 
<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>

<div class="ZoomContainer" uuid="0d89dc96-434b-4c35-ab61-21388a3f87e4" style="position: absolute; top: 363.738953px; left: 213.033081px; height: 839.742615px; width: 508.014679px; z-index: 999;">…</div>
[Log] Změna 7: – "childList" – 
<div class="zoomWindowContainer" style="width: 350px;">…</div>

<div class="zoomWindowContainer" style="width: 350px;">…</div>
[Log] Změna 8: – "attributes" – 
<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>

<div class="zoomWindow" style="z-index: 100; display: none; position: absolute; height: 350px; width: 350px; border: 2px solid rgb(136, 136, 136); background-size: 1000px 1653px; background-position: 0px 0px; background-repeat: no-repeat; background-color: rgb(255, 255, 255); overflow: hidden; background-image: url("https://czimg-dev1.www2.peterman.cz/66611-large_default/40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.jpg");"></div>
[Log] Změna 275: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 9: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 276: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 10: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000643%; overflow: hidden;"></div>
[Log] Změna 277: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>
[Log] Změna 11: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>
[Log] Změna 278: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>
[Log] Změna 12: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>

<div class="progress-bar" role="progressbar" style="width: 100.000229%; overflow: hidden;"></div>
[Log] Změna 279: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 13: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)
[Log] Změna 280: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 14: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x39)
[Log] Změna 281: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 282: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 15: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log] Změna 16: – "attributes" – 
<div class="progress-bar" role="progressbar" style="width: 100%;"></div>

<div class="progress-bar" role="progressbar" style="width: 100%;"></div>
[Log]   Po změně: 1 bloků, 1 viditelných (x16)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 74)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 85)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 91)
[Log] PriceInquiry: Cenový element nenalezen, ponechávám současný stav (front.js, line 119)