[Log] Current quantity: – "1" (front.js, line 324)
[Log] PriceInquiry: Variant select nenalezen (pokus 5) (front.js, line 106)
[Log] PriceInquiry: Variant select nenalezen ani po 5 pokusech (front.js, line 112)
[Log] PriceInquiry: updateProduct event (front.js, line 50, x2)
[Log] PriceInquiry: updatedProduct event (front.js, line 54)
[Log] PriceInquiry: *** handleProductUpdate CALLED *** – {product_prices: "  <div class=\"product-prices js-product-prices\">↵ …                                 </div>↵  </div>↵", product_cover_thumbnails: "<div class=\"images-container js-images-container\">…          </aside>↵      </div>↵  ↵</div>↵</div>↵", product_customization: "<section class=\"product-customization js-product-c…v>↵      </form>↵      ↵↵    </div>↵  </section>↵", …} (front.js, line 188)
{product_prices: "  <div class=\"product-prices js-product-prices\">↵ …                                 </div>↵  </div>↵", product_cover_thumbnails: "<div class=\"images-container js-images-container\">…          </aside>↵      </div>↵  ↵</div>↵</div>↵", product_customization: "<section class=\"product-customization js-product-c…v>↵      </form>↵      ↵↵    </div>↵  </section>↵", product_details: "<div class=\"js-product-details tab-pane fade\"↵    …v>↵    ↵↵    ↵        ↵↵        ↵        ↵</div>↵", product_variants: "<div class=\"product-variants js-product-variants\">…              </ul>↵          </div>↵     </div>↵", …}Object
[Log] PriceInquiry: Okamžitá kontrola... (front.js, line 191)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 251)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 273)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] PriceInquiry: Skrývám "cena na dotaz" (front.js, line 310)
[Log] PriceInquiry: applyCssHiding: – false (front.js, line 324)
[Log] PriceInquiry: CSS pro skrytí odstraněno, elementy obnoveny (front.js, line 354)
[Log] PriceInquiry: Blok skryt (front.js, line 319)
[Log] Current quantity: – "1" (front.js, line 324)
[Log] PriceInquiry: *** PŘIDÁN element s cenou *** –  (front.js, line 161)
<div class="product-prices js-product-prices">…</div>

<div class="product-prices js-product-prices">…</div>
[Log] PriceInquiry: *** MUTATION v add-to-cart *** – "childList" (front.js, line 153, x2)
[Log] PriceInquiry: MutationObserver detekoval změnu, spouštím kontrolu... (front.js, line 169)
[Log] PriceInquiry: Kontrola po 100ms... (front.js, line 196)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 251)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 273)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] PriceInquiry: *** handleProductUpdate CALLED *** – {reason: "mutation_observer"} (front.js, line 188)
[Log] PriceInquiry: Okamžitá kontrola... (front.js, line 191)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 251)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 273)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] Current quantity: – "1" (front.js, line 324)
[Log] PriceInquiry: Kontrola po 100ms... (front.js, line 196)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 251)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 273)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] PriceInquiry: Kontrola po 500ms... (front.js, line 202)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 251)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 273)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] PriceInquiry: Kontrola po 500ms... (front.js, line 202)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 251)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 273)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] PriceInquiry: Kontrola po 1000ms... (front.js, line 208)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 251)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 273)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] PriceInquiry: Kontrola po 1000ms... (front.js, line 208)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 214)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 225)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 231)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 251)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 273)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 281)
[Log] parameter changed (front.js, line 85)
[Error] AJAX request failed: – SyntaxError: The string did not match the expected pattern.
SyntaxError: The string did not match the expected pattern.
	(anonymní funkce) (front.js:285)