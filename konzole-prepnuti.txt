[Log] PriceInquiry: updateProduct event (front.js, line 50, x2)
[Log] PriceInquiry: updatedProduct event (front.js, line 54)
[Log] PriceInquiry: handleProductUpdate called – {product_prices: "  <div class=\"product-prices js-product-prices\">↵ …                                 </div>↵  </div>↵", product_cover_thumbnails: "<div class=\"images-container js-images-container\">…          </aside>↵      </div>↵  ↵</div>↵</div>↵", product_customization: "<section class=\"product-customization js-product-c…v>↵      </form>↵      ↵↵    </div>↵  </section>↵", …} (front.js, line 76)
{product_prices: "  <div class=\"product-prices js-product-prices\">↵ …                                 </div>↵  </div>↵", product_cover_thumbnails: "<div class=\"images-container js-images-container\">…          </aside>↵      </div>↵  ↵</div>↵</div>↵", product_customization: "<section class=\"product-customization js-product-c…v>↵      </form>↵      ↵↵    </div>↵  </section>↵", product_details: "<div class=\"js-product-details tab-pane fade\"↵    …v>↵    ↵↵    ↵        ↵↵        ↵        ↵</div>↵", product_variants: "<div class=\"product-variants js-product-variants\">…              </ul>↵          </div>↵     </div>↵", …}Object
[Log] Current quantity: – "1" (front.js, line 324)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 89)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 100)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 106)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 126)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 148)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 156)
[Log] PriceInquiry: Skrývám "cena na dotaz" (front.js, line 185)
[Log] PriceInquiry: applyCssHiding: – false (front.js, line 199)
[Log] PriceInquiry: CSS pro skrytí odstraněno, elementy obnoveny (front.js, line 229)
[Log] PriceInquiry: Blok skryt (front.js, line 194)
[Log] Current quantity: – "1" (front.js, line 324)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 89)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 100)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 106)
[Log] PriceInquiry: Nalezen cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 126)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 148)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 156)
[Log] parameter changed (front.js, line 85)
[Error] AJAX request failed: – SyntaxError: The string did not match the expected pattern.
SyntaxError: The string did not match the expected pattern.
	(anonymní funkce) (front.js:285)