[Log] PriceInquiry: updateProduct event (front.js, line 50, x2)
[Log] PriceInquiry: updatedProduct event (front.js, line 54)
[Log] PriceInquiry: *** handleProductUpdate CALLED *** – {product_prices: "  <div class=\"product-prices js-product-prices\">↵ …                                 </div>↵  </div>↵", product_cover_thumbnails: "<div class=\"images-container js-images-container\">…          </aside>↵      </div>↵  ↵</div>↵</div>↵", product_customization: "<section class=\"product-customization js-product-c…v>↵      </form>↵      ↵↵    </div>↵  </section>↵", …} (front.js, line 188)
{product_prices: "  <div class=\"product-prices js-product-prices\">↵ …                                 </div>↵  </div>↵", product_cover_thumbnails: "<div class=\"images-container js-images-container\">…          </aside>↵      </div>↵  ↵</div>↵</div>↵", product_customization: "<section class=\"product-customization js-product-c…v>↵      </form>↵      ↵↵    </div>↵  </section>↵", product_details: "<div class=\"js-product-details tab-pane fade\"↵    …v>↵    ↵↵    ↵        ↵↵        ↵        ↵</div>↵", product_variants: "<div class=\"product-variants js-product-variants\">…              </ul>↵          </div>↵     </div>↵", …}Object
[Log] PriceInquiry: Okamžitá kontrola... (front.js, line 191)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – true (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 297)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 325)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Skrývám blok (cena není nulová) (front.js, line 341)
[Log] PriceInquiry: Skrývám "cena na dotaz" (front.js, line 365)
[Log] PriceInquiry: applyCssHiding: – false (front.js, line 379)
[Log] PriceInquiry: CSS pro skrytí odstraněno, elementy obnoveny (front.js, line 409)
[Log] PriceInquiry: Blok skryt (front.js, line 374)
[Log] Current quantity: – "1" (front.js, line 324)
[Log] PriceInquiry: *** PŘIDÁN element s cenou *** –  (front.js, line 161)
<div class="product-prices js-product-prices">…</div>

<div class="product-prices js-product-prices">…</div>
[Log] PriceInquiry: *** MUTATION v add-to-cart *** – "childList" (front.js, line 153, x2)
[Log] PriceInquiry: MutationObserver detekoval změnu, spouštím kontrolu... (front.js, line 169)
[Log] PriceInquiry: Kontrola po 100ms... (front.js, line 196)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 297)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          ↵                                      91,40 Kč↵                      ↵↵                  ↵↵        ↵                …" (front.js, line 325)
"

        
          
                                      91,40 Kč
                      

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] Current quantity: – "1" (front.js, line 324)
[Log] PriceInquiry: *** handleProductUpdate CALLED *** – {reason: "mutation_observer"} (front.js, line 188)
[Log] PriceInquiry: Okamžitá kontrola... (front.js, line 191)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Kontrola po 100ms... (front.js, line 196)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Kontrola po 300ms... (front.js, line 202)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Kontrola po 300ms... (front.js, line 202)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Kontrola po 500ms... (front.js, line 208)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Kontrola po 500ms... (front.js, line 208)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Kontrola po 1000ms... (front.js, line 214)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Kontrola po 1000ms... (front.js, line 214)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] parameter changed (front.js, line 85)
[Log] PriceInquiry: Finální kontrola po 2000ms... (front.js, line 220)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Log] PriceInquiry: Finální kontrola po 2000ms... (front.js, line 220)
[Log] PriceInquiry: Spouštím kontrolu ceny... (front.js, line 226)
[Log] PriceInquiry: Počáteční cena z PHP: – "0" (front.js, line 237)
[Log] PriceInquiry: Blok je aktuálně viditelný: – false (front.js, line 243)
[Log] PriceInquiry: === DEBUG všech cenových elementů === (front.js, line 256)
[Log]   .product-price: 1 elementů (front.js, line 259)
[Log]     [0] text: "91,40 Kč" visible: false (front.js, line 263)
[Log]   .product-prices .price: 0 elementů (front.js, line 259)
[Log]   .current-price .price: 0 elementů (front.js, line 259)
[Log] PriceInquiry: Viditelný element nenalezen, hledám jakýkoliv... (front.js, line 288)
[Log] PriceInquiry: Nalezen JAKÝKOLIV cenový element: – ".product-price" – "text:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 297)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Detekovaná cena: – 91.4 – "z textu:" – "↵↵        ↵          91,40 Kč↵↵                  ↵↵        ↵                  ↵      " (front.js, line 325)
"

        
          91,40 Kč

                  

        
                  
      "
[Log] PriceInquiry: Je cena nulová? – false (front.js, line 333)
[Log] PriceInquiry: Ponechávám současný stav (front.js, line 344)
[Error] AJAX request failed: – SyntaxError: The string did not match the expected pattern. — front.js:131
SyntaxError: The string did not match the expected pattern. — front.js:131
	(anonymní funkce) (front.js:285)